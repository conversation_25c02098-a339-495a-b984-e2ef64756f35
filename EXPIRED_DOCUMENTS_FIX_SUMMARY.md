# Expired Documents Admin Dashboard Fix

## 🔍 Issues Identified

### 1. **Incomplete Document Type Filtering**
The repository was using a hardcoded list of document types that was missing several types:

**Missing Types:**
- `intakedocument`
- `projectietekening`
- `beveiligingsplan`
- `kabeltekeningen`
- `vrije_documenten`

**Incorrect Type:**
- `klantnaam_zoeken` (should be removed as mentioned)

### 2. **Frontend Double Filtering**
The admin dashboard was applying additional filtering on the frontend that was inconsistent:
- Initial load: No filtering
- After event creation: Filtered by `doc.status === 'active'`

### 3. **Missing Customer Information**
The frontend expected `customer_name` field but it wasn't being provided by the backend.

## 🔧 Fixes Applied

### 1. **Updated Document Repository**
**File:** `backend/app/repositories/document_repository.py`

**Changes:**
- Replaced hardcoded document type lists with `ALLOWED_DOCUMENT_TYPES` from the model
- Added eager loading of customer data using `joinedload(Document.customer)`
- Both `get_upcoming_expirations()` and `get_expired_documents()` methods updated

```python
# Before
main_document_types = [
    "offerte",
    "klantnaam_zoeken",  # This was wrong
    "werkbon",
    # ... missing several types
]

# After
from app.models.document import ALLOWED_DOCUMENT_TYPES
main_document_types = ALLOWED_DOCUMENT_TYPES
```

### 2. **Enhanced Document Model**
**File:** `backend/app/models/document.py`

**Changes:**
- Updated `to_dict()` method to include `customer_name` when customer relationship is loaded
- Added logic to safely access customer name

```python
# Get customer name if customer relationship is loaded
customer_name = None
if hasattr(self, 'customer') and self.customer:
    customer_name = self.customer.name

return {
    # ... other fields
    "customer_name": customer_name,
    # ... rest of fields
}
```

### 3. **Fixed Frontend Filtering**
**File:** `frontend/src/pages/AdminDashboard.tsx`

**Changes:**
- Removed inconsistent frontend filtering after event creation
- Backend now handles all filtering consistently

```typescript
// Before
setUpcomingDocuments(expirationsResponse?.documents.filter(doc => doc.status === 'active') || []);
setExpiredDocuments(expiredResponse?.documents.filter(doc => doc.status === 'active') || []);

// After
setUpcomingDocuments(expirationsResponse?.documents || []);
setExpiredDocuments(expiredResponse?.documents || []);
```

## 🎯 Expected Behavior Now

### 1. **Expired Documents Display**
- All expired documents with `status = "active"` will appear on admin dashboard
- Includes all document types defined in `ALLOWED_DOCUMENT_TYPES`
- Shows customer name instead of just customer ID

### 2. **Event Creation from Expired Documents**
- Click on expired document → creates event for that document type
- Event completion → new document upload marks old document as `inactive`
- Expired document disappears from dashboard when replaced

### 3. **Document Lifecycle**
```
1. Document expires (expiry_date < now) → Shows on admin dashboard
2. Admin clicks → Creates event
3. User completes event → Uploads new document
4. New document created → Old document marked as inactive
5. Expired document disappears from dashboard
```

## 🧪 Testing the Fix

### 1. **Check Expired Documents Endpoint**
```bash
curl -X GET "http://localhost:5000/api/documents/expired-documents" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 2. **Verify Document Types**
Ensure all these types appear when expired:
- offerte
- werkbon
- onderhoudsbon
- onderhoudscontract
- meldkamercontract
- beveiligingscertificaat
- intakedocument
- projectietekening
- beveiligingsplan
- kabeltekeningen
- checklist oplevering installatie
- vrije_documenten

### 3. **Test Complete Workflow**
1. Create a document with past expiry date
2. Check it appears on admin dashboard
3. Create event from expired document
4. Complete event with new document
5. Verify old document disappears

## 🔄 Document Status Flow

```
Active Document (status="active") 
    ↓ (expires)
Expired Document (status="active", expiry_date < now)
    ↓ (new document uploaded)
Inactive Document (status="inactive")
```

**Key Points:**
- Expired documents still have `status="active"` until replaced
- Only when a new document of the same type is uploaded does the old one become `inactive`
- The admin dashboard shows expired documents that are still `active`

## 🚀 Benefits

1. **Complete Coverage:** All document types now appear when expired
2. **Better UX:** Customer names displayed instead of IDs
3. **Consistent Behavior:** Same filtering logic everywhere
4. **Proper Lifecycle:** Documents properly transition through states
5. **Maintainable:** Uses centralized document type definitions

The expired documents should now appear correctly on the admin dashboard, allowing you to create events for them and manage the document renewal process properly!
