# Render Deployment Guide

This guide will help you deploy your Customer Management application to Render, a modern cloud platform that provides encryption at rest by default.

## 🔒 Security Note

**Encryption has been removed from the application** since <PERSON><PERSON> provides:
- **Encryption at rest** for all data stored in databases
- **Encryption in transit** with automatic HTTPS/TLS
- **Network isolation** and security best practices

## 🚀 Quick Deployment Steps

### Option 1: Using render.yaml (Recommended)

1. **Push your code to GitHub** (if not already done)
2. **Connect to Render**:
   - Go to [render.com](https://render.com) and sign up/login
   - Connect your GitHub account
   - Select your repository

3. **Deploy using Blueprint**:
   - Render will automatically detect the `render.yaml` file
   - Click "Apply" to create all services at once

### Option 2: Manual Setup

#### Step 1: Create PostgreSQL Database

1. In Render Dashboard, click "New +"
2. Select "PostgreSQL"
3. Configure:
   - **Name**: `customer-management-db`
   - **Database**: `customer_management`
   - **User**: `customer_management_user`
   - **Region**: Choose closest to your users
   - **Plan**: Free (or paid for production)

#### Step 2: Deploy Backend

1. Click "New +" → "Web Service"
2. Connect your repository
3. Configure:
   - **Name**: `customer-management-backend`
   - **Environment**: `Python 3`
   - **Region**: Same as database
   - **Branch**: `main` (or your default branch)
   - **Root Directory**: `backend`
   - **Build Command**: `pip install -r requirements.txt`
   - **Start Command**: `python app.py`

#### Step 3: Set Environment Variables

In your backend service settings, add these environment variables:

**Required Variables:**
```
FLASK_ENV=production
FLASK_DEBUG=false
DATABASE_URL=[Auto-filled from database connection]
SECRET_KEY=[Generate a secure random string]
JWT_SECRET_KEY=[Generate a secure random string]
```

**Firebase Configuration:**
```
FIREBASE_CREDENTIALS_JSON=[Your Firebase service account JSON]
```

**Email Configuration (if using):**
```
MAIL_SERVER=your-smtp-server.com
MAIL_PORT=587
MAIL_USE_TLS=true
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
```

## 🔧 Environment Variable Generation

### Generate Secret Keys
```bash
# In Python
import secrets
print("SECRET_KEY=" + secrets.token_hex(32))
print("JWT_SECRET_KEY=" + secrets.token_hex(32))
```

### Firebase Setup
1. Go to Firebase Console → Project Settings → Service Accounts
2. Generate new private key (downloads JSON file)
3. Copy the entire JSON content to `FIREBASE_CREDENTIALS_JSON` variable

## 🗄️ Database Migration

After deployment, you'll need to initialize your database:

1. **Access your service shell** (in Render dashboard)
2. **Run migrations**:
   ```bash
   cd backend
   python -c "from app import create_app, db; app, _ = create_app(); app.app_context().push(); db.create_all()"
   ```

## 🌐 Frontend Deployment (Optional)

If deploying frontend to Render as well:

1. Click "New +" → "Static Site"
2. Configure:
   - **Name**: `customer-management-frontend`
   - **Root Directory**: `frontend`
   - **Build Command**: `npm install && npm run build`
   - **Publish Directory**: `dist`

**Frontend Environment Variables:**
```
VITE_API_URL=https://your-backend-service.onrender.com
VITE_FIREBASE_API_KEY=your-firebase-api-key
VITE_FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=your-project-id
VITE_FIREBASE_STORAGE_BUCKET=your-project.appspot.com
VITE_FIREBASE_MESSAGING_SENDER_ID=your-sender-id
VITE_FIREBASE_APP_ID=your-app-id
VITE_FIREBASE_MEASUREMENT_ID=your-measurement-id
```

## 🔍 Health Check

Your backend includes a health check endpoint at `/api/health` that Render will use to monitor your service.

## 📊 Monitoring & Logs

- **Logs**: Available in Render dashboard under your service
- **Metrics**: CPU, memory, and request metrics in dashboard
- **Alerts**: Set up notifications for service issues

## 🚨 Production Considerations

### Performance
- **Upgrade to paid plan** for better performance and no sleep mode
- **Enable autoscaling** for high traffic
- **Use CDN** for static assets

### Security
- **Environment variables** are encrypted at rest
- **HTTPS** is automatic with custom domains
- **Network isolation** between services
- **Regular security updates** handled by Render

### Backup
- **Database backups** are automatic on paid plans
- **Point-in-time recovery** available
- **Export data** regularly for additional safety

## 🔧 Troubleshooting

### Common Issues

1. **Build Failures**:
   - Check build logs in Render dashboard
   - Ensure all dependencies are in requirements.txt
   - Verify Python version compatibility

2. **Database Connection Issues**:
   - Verify DATABASE_URL is correctly set
   - Check database service is running
   - Ensure both services are in same region

3. **Environment Variable Issues**:
   - Double-check all required variables are set
   - Verify Firebase JSON is properly formatted
   - Ensure no trailing spaces in values

### Getting Help
- **Render Documentation**: [render.com/docs](https://render.com/docs)
- **Community Forum**: [community.render.com](https://community.render.com)
- **Support**: Available through dashboard

## 🎉 Post-Deployment

After successful deployment:

1. **Test all functionality** thoroughly
2. **Set up monitoring** and alerts
3. **Configure custom domain** (optional)
4. **Set up CI/CD** for automatic deployments
5. **Document your deployment** for team members

Your application is now deployed with enterprise-grade security including encryption at rest, automatic HTTPS, and network isolation!
