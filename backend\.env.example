# Database Configuration
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/amspm_db
DATABASE_PASSWORD=  # Leave blank or set manually when running the script

# Security
SECRET_KEY=your_secret_key_here
# Set to True only in development, False in production
FLASK_DEBUG=False

# Rate limiting configuration (custom rate limiter)
# Set to False to disable rate limiting entirely
RATE_LIMIT_ENABLED=True

# Security settings
# Set to True to sanitize API responses (hide sensitive data like permissions and document types)
# Set to False only if you need to debug these specific fields
SANITIZE_RESPONSES=True

# Encryption is handled by <PERSON><PERSON>'s encryption at rest
# No application-level encryption configuration needed

# Firebase Configuration
# Option 1: Path to Firebase credentials file (recommended for development)
FIREBASE_CREDENTIALS_PATH=./secrets/firebase-service-account-key.json

# Option 2: Firebase credentials as JSON string (recommended for production)
# FIREBASE_CREDENTIALS_JSON={"type":"service_account","project_id":"your-project-id","private_key_id":"your-private-key-id","private_key":"your-private-key","client_email":"*****************","client_id":"your-client-id","auth_uri":"https://accounts.google.com/o/oauth2/auth","token_uri":"https://oauth2.googleapis.com/token","auth_provider_x509_cert_url":"https://www.googleapis.com/oauth2/v1/certs","client_x509_cert_url":"your-client-cert-url","universe_domain":"googleapis.com"}

# Admin User
ADMIN_EMAIL=<EMAIL>

# Logging
LOG_FILE=./logs/app.log

# Cache Configuration
CACHE_TYPE=SimpleCache  # Options: SimpleCache, RedisCache
REDIS_URL=redis://localhost:6379/0
CACHE_TIMEOUT=3600  # 1 hour
CACHE_KEY_PREFIX=customer_mgmt_

# Optional e-Boekhouden settings
EBOEKHOUDEN_API_KEY=your_api_key
EBOEKHOUDEN_API_URL=https://api.e-boekhouden.nl
EBOEKHOUDEN_API_USERNAME=your_username
EBOEKHOUDEN_API_PASSWORD=your_password