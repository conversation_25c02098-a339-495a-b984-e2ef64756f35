from flask import Blueprint, request, jsonify
from app.services.user_service import UserService
from app.utils.security import token_required, role_required, roles_required
from app.utils.rate_limit import rate_limit
from app.utils.cache_decorators import cached_list, cached_detail, cached_search

from app.schemas.user_schema import user_schema, users_schema, user_create_schema
import logging
from marshmallow import ValidationError

user_bp = Blueprint("user", __name__)

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

user_service = UserService()

@user_bp.route("", methods=["GET"])
@role_required("administrator")
@rate_limit("60/minute")
@cached_list(entity_type="user", timeout=300)  # Cache for 5 minutes
def get_all_users():
    page = int(request.args.get("page", 1))
    per_page = int(request.args.get("per_page", 20))
    try:
        users, total = user_service.get_all_users(page, per_page)
        logger.info(f"Fetched {len(users)} users for page {page}")
        return jsonify({"users": users, "total": total, "page": page, "per_page": per_page}), 200
    except Exception as e:
        logger.error(f"Failed to fetch users: {str(e)}")
        return jsonify({"error": str(e)}), 500

@user_bp.route("/<int:user_id>", methods=["GET"])
@role_required("administrator")
@cached_detail(entity_type="user", timeout=300)  # Cache for 5 minutes
def get_user(user_id):
    try:
        user = user_service.get_user_by_id(user_id)
        return jsonify(user), 200
    except Exception as e:
        logger.error(f"Failed to fetch user {user_id}: {str(e)}")
        return jsonify({"error": str(e)}), 404

@user_bp.route("", methods=["POST"])
@role_required("administrator")
@rate_limit("60/minute")
def create_user():
    data = request.get_json()
    if not data:
        return jsonify({"error": "No data provided"}), 400

    try:
        # Validate the input data using the user creation schema
        errors = user_create_schema.validate(data)
        if errors:
            logger.warning(f"Create user validation failed: {errors}")
            return jsonify({"error": "Validation failed", "details": errors}), 400

        # Get current user and request info for audit log
        current_user = request.current_user
        ip_address = request.remote_addr
        user_agent = request.headers.get("User-Agent")

        user = user_service.create_user(
            data["email"],
            data["password"],
            data["role"],
            data.get("name"),
            current_user_id=current_user.id,
            ip_address=ip_address,
            user_agent=user_agent
        )
        logger.info(f"Created user: {user['email']}")
        return jsonify(user), 201
    except ValidationError as e:
        logger.warning(f"Create user validation error: {e.messages}")
        return jsonify({"error": "Validation error", "details": e.messages}), 400
    except Exception as e:
        logger.error(f"Failed to create user: {str(e)}")
        return jsonify({"error": str(e)}), 400

@user_bp.route("/<int:user_id>/role", methods=["PUT"])
@role_required("administrator")
@rate_limit("10/minute")
def update_user_role(user_id):
    data = request.get_json()
    if not data:
        return jsonify({"error": "No data provided"}), 400

    try:
        # Validate the role field
        if "role" not in data:
            return jsonify({"error": "Role is required"}), 400

        # Validate the role value
        if data["role"] not in ["administrator", "verkoper", "monteur"]:
            return jsonify({"error": "Invalid role. Must be one of: administrator, verkoper, monteur"}), 400

        # Get current user and request info for audit log
        current_user = request.current_user
        ip_address = request.remote_addr
        user_agent = request.headers.get("User-Agent")

        user = user_service.update_user_role(
            user_id,
            data["role"],
            current_user_id=current_user.id,
            ip_address=ip_address,
            user_agent=user_agent
        )
        logger.info(f"Updated role for user {user_id} to {data['role']}")
        return jsonify(user), 200
    except ValidationError as e:
        logger.warning(f"Update user role validation error: {e.messages}")
        return jsonify({"error": "Validation error", "details": e.messages}), 400
    except Exception as e:
        logger.error(f"Failed to update user role: {str(e)}")
        return jsonify({"error": str(e)}), 400

@user_bp.route("/<int:user_id>/name", methods=["PUT"])
@role_required("administrator")
@rate_limit("20/minute")
def update_user_name(user_id):
    data = request.get_json()
    if not data:
        return jsonify({"error": "No data provided"}), 400

    try:
        # Validate the name field
        if "name" not in data:
            return jsonify({"error": "Name is required"}), 400

        # Get current user and request info for audit log
        current_user = request.current_user
        ip_address = request.remote_addr
        user_agent = request.headers.get("User-Agent")

        user = user_service.update_user_name(
            user_id,
            data["name"],
            current_user_id=current_user.id,
            ip_address=ip_address,
            user_agent=user_agent
        )
        logger.info(f"Updated name for user {user_id} to {data['name']}")
        return jsonify(user), 200
    except ValidationError as e:
        logger.warning(f"Update user name validation error: {e.messages}")
        return jsonify({"error": "Validation error", "details": e.messages}), 400
    except Exception as e:
        logger.error(f"Failed to update user name: {str(e)}")
        return jsonify({"error": str(e)}), 400

@user_bp.route("/<int:user_id>", methods=["DELETE"])
@role_required("administrator")
@rate_limit("60/minute")
def delete_user(user_id):
    try:
        # Get current user and request info for audit log
        current_user = request.current_user
        ip_address = request.remote_addr
        user_agent = request.headers.get("User-Agent")

        user_service.delete_user(
            user_id,
            current_user_id=current_user.id,
            ip_address=ip_address,
            user_agent=user_agent
        )
        logger.info(f"Deleted user {user_id}")
        return jsonify({"message": "User deleted"}), 200
    except Exception as e:
        logger.error(f"Failed to delete user {user_id}: {str(e)}")
        return jsonify({"error": str(e)}), 404





@user_bp.route("/profile/name", methods=["PUT"])
@token_required
@rate_limit("20/minute")
def update_current_user_name():
    data = request.get_json()
    if not data:
        return jsonify({"error": "No data provided"}), 400

    try:
        # Validate the name field
        if "name" not in data:
            return jsonify({"error": "Name is required"}), 400

        # Get current user and request info for audit log
        current_user = request.current_user
        ip_address = request.remote_addr
        user_agent = request.headers.get("User-Agent")

        user = user_service.update_user_name(
            current_user.id,
            data["name"],
            current_user_id=current_user.id,
            ip_address=ip_address,
            user_agent=user_agent
        )
        logger.info(f"User {current_user.id} updated their name to {data['name']}")
        return jsonify(user), 200
    except ValidationError as e:
        logger.warning(f"Update user name validation error: {e.messages}")
        return jsonify({"error": "Validation error", "details": e.messages}), 400
    except Exception as e:
        logger.error(f"Failed to update user name: {str(e)}")
        return jsonify({"error": str(e)}), 400

@user_bp.route("/profile/password", methods=["PUT"])
@token_required
@rate_limit("10/minute")
def update_current_user_password():
    data = request.get_json()
    if not data:
        return jsonify({"error": "No data provided"}), 400

    try:
        # Validate the password field
        if "password" not in data:
            return jsonify({"error": "Password is required"}), 400

        # Validate password strength
        password = data["password"]
        if len(password) < 8:
            return jsonify({"error": "Password must be at least 8 characters long"}), 400
        if not any(c.isupper() for c in password):
            return jsonify({"error": "Password must contain at least one uppercase letter"}), 400
        if not any(c.islower() for c in password):
            return jsonify({"error": "Password must contain at least one lowercase letter"}), 400
        if not any(c.isdigit() for c in password):
            return jsonify({"error": "Password must contain at least one number"}), 400

        # Get current user and request info for audit log
        current_user = request.current_user
        ip_address = request.remote_addr
        user_agent = request.headers.get("User-Agent")

        user = user_service.update_user_password(
            current_user.id,
            data["password"],
            current_user_id=current_user.id,
            ip_address=ip_address,
            user_agent=user_agent
        )
        logger.info(f"User {current_user.id} updated their password")
        return jsonify({"message": "Password updated successfully"}), 200
    except Exception as e:
        logger.error(f"Failed to update user password: {str(e)}")
        return jsonify({"error": str(e)}), 400