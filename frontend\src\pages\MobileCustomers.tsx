import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { getCustomers, deleteCustomer } from "../services/customerService";
import { Customer } from "../types/customer";
import MobileCard, { MobileListItem, MobileActionButton } from "../components/MobileCard";
import { MobileInput, MobileModal } from "../components/MobileForm";
import { FaSearch, FaPlus, FaUser, FaPhone, FaEnvelope, FaMapMarkerAlt, FaEdit, FaTrash } from "react-icons/fa";

const MobileCustomers: React.FC = () => {
  const navigate = useNavigate();
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [filteredCustomers, setFilteredCustomers] = useState<Customer[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [deleting, setDeleting] = useState(false);

  useEffect(() => {
    fetchCustomers();
  }, []);

  useEffect(() => {
    filterCustomers();
  }, [customers, searchTerm]);

  const fetchCustomers = async () => {
    try {
      setLoading(true);
      const response = await getCustomers();
      setCustomers(response.customers || []);
    } catch (error) {
      console.error("Error fetching customers:", error);
    } finally {
      setLoading(false);
    }
  };

  const filterCustomers = () => {
    if (!searchTerm.trim()) {
      setFilteredCustomers(customers);
      return;
    }

    const filtered = customers.filter(customer =>
      customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      customer.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (customer.phone && customer.phone.includes(searchTerm))
    );
    setFilteredCustomers(filtered);
  };

  const handleDeleteCustomer = async () => {
    if (!selectedCustomer) return;

    try {
      setDeleting(true);
      await deleteCustomer(selectedCustomer.id);
      await fetchCustomers();
      setShowDeleteModal(false);
      setSelectedCustomer(null);
    } catch (error) {
      console.error("Error deleting customer:", error);
    } finally {
      setDeleting(false);
    }
  };

  const formatAddress = (customer: Customer) => {
    const parts = [customer.address, customer.city, customer.postal_code].filter(Boolean);
    return parts.join(", ");
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-amspm-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h1 className="text-xl font-bold text-amspm-text dark:text-dark-text">
          Customers ({filteredCustomers.length})
        </h1>
        <MobileActionButton
          onClick={() => navigate("/customers/new")}
          size="sm"
        >
          <FaPlus className="mr-2" />
          Add
        </MobileActionButton>
      </div>

      {/* Search */}
      <MobileCard padding="sm">
        <div className="relative">
          <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-amspm-text-light dark:text-dark-text-light" />
          <input
            type="text"
            placeholder="Search customers..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-amspm-light-gray dark:border-dark-border rounded-lg bg-white dark:bg-dark-secondary text-amspm-text dark:text-dark-text focus:ring-2 focus:ring-amspm-primary focus:border-transparent"
          />
        </div>
      </MobileCard>

      {/* Customer List */}
      {filteredCustomers.length === 0 ? (
        <MobileCard>
          <div className="text-center py-8">
            <FaUser className="text-4xl text-amspm-text-light dark:text-dark-text-light mx-auto mb-4" />
            <h3 className="text-lg font-medium text-amspm-text dark:text-dark-text mb-2">
              {searchTerm ? "No customers found" : "No customers yet"}
            </h3>
            <p className="text-amspm-text-light dark:text-dark-text-light mb-4">
              {searchTerm 
                ? "Try adjusting your search terms" 
                : "Get started by adding your first customer"
              }
            </p>
            {!searchTerm && (
              <MobileActionButton
                onClick={() => navigate("/customers/new")}
                size="sm"
              >
                <FaPlus className="mr-2" />
                Add Customer
              </MobileActionButton>
            )}
          </div>
        </MobileCard>
      ) : (
        <div className="space-y-3">
          {filteredCustomers.map((customer) => (
            <MobileCard key={customer.id} className="relative">
              <div className="space-y-3">
                {/* Customer Header */}
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <h3 className="font-medium text-amspm-text dark:text-dark-text">
                      {customer.name}
                    </h3>
                    <p className="text-sm text-amspm-text-light dark:text-dark-text-light">
                      Customer #{customer.id}
                    </p>
                  </div>
                  <div className="flex space-x-2">
                    <button
                      onClick={() => navigate(`/customers/${customer.id}/edit`)}
                      className="p-2 text-amspm-primary dark:text-dark-accent hover:bg-amspm-light-gray dark:hover:bg-dark-hover rounded-lg transition-colors"
                    >
                      <FaEdit />
                    </button>
                    <button
                      onClick={() => {
                        setSelectedCustomer(customer);
                        setShowDeleteModal(true);
                      }}
                      className="p-2 text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-colors"
                    >
                      <FaTrash />
                    </button>
                  </div>
                </div>

                {/* Customer Details */}
                <div className="space-y-2">
                  {customer.email && (
                    <div className="flex items-center space-x-2 text-sm text-amspm-text-light dark:text-dark-text-light">
                      <FaEnvelope className="text-xs flex-shrink-0" />
                      <span className="truncate">{customer.email}</span>
                    </div>
                  )}
                  
                  {customer.phone && (
                    <div className="flex items-center space-x-2 text-sm text-amspm-text-light dark:text-dark-text-light">
                      <FaPhone className="text-xs flex-shrink-0" />
                      <span>{customer.phone}</span>
                    </div>
                  )}
                  
                  {formatAddress(customer) && (
                    <div className="flex items-center space-x-2 text-sm text-amspm-text-light dark:text-dark-text-light">
                      <FaMapMarkerAlt className="text-xs flex-shrink-0" />
                      <span className="truncate">{formatAddress(customer)}</span>
                    </div>
                  )}
                </div>

                {/* Action Button */}
                <MobileActionButton
                  onClick={() => navigate(`/customers/${customer.id}`)}
                  variant="secondary"
                  size="sm"
                  fullWidth
                >
                  View Details
                </MobileActionButton>
              </div>
            </MobileCard>
          ))}
        </div>
      )}

      {/* Delete Confirmation Modal */}
      <MobileModal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        title="Delete Customer"
      >
        <div className="space-y-4">
          <p className="text-amspm-text dark:text-dark-text">
            Are you sure you want to delete <strong>{selectedCustomer?.name}</strong>?
            This action cannot be undone.
          </p>
          
          <div className="flex space-x-3">
            <MobileActionButton
              onClick={() => setShowDeleteModal(false)}
              variant="secondary"
              fullWidth
            >
              Cancel
            </MobileActionButton>
            <MobileActionButton
              onClick={handleDeleteCustomer}
              variant="danger"
              disabled={deleting}
              fullWidth
            >
              {deleting ? "Deleting..." : "Delete"}
            </MobileActionButton>
          </div>
        </div>
      </MobileModal>
    </div>
  );
};

export default MobileCustomers;
