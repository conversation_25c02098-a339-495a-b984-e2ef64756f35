services:
  # PostgreSQL Database
  - type: pserv
    name: customer-management-db
    env: python
    plan: free
    region: frankfurt
    databases:
      - name: customer_management
        user: customer_management_user

  # Backend API Service
  - type: web
    name: customer-management-backend
    env: python
    region: frankfurt
    plan: free
    buildCommand: |
      cd backend
      pip install -r requirements.txt
    startCommand: |
      cd backend
      gunicorn --bind 0.0.0.0:$PORT app:app
    envVars:
      - key: FLASK_ENV
        value: production
      - key: FLASK_DEBUG
        value: false
      - key: DATABASE_URL
        fromDatabase:
          name: customer-management-db
          property: connectionString
      - key: SECRET_KEY
        generateValue: true
      - key: JWT_SECRET_KEY
        generateValue: true
      - key: FIREBASE_CREDENTIALS_JSON
        sync: false  # Set this manually in Render dashboard
      - key: MAIL_SERVER
        sync: false
      - key: MAIL_PORT
        value: 587
      - key: MAIL_USE_TLS
        value: true
      - key: MAIL_USERNAME
        sync: false
      - key: <PERSON>IL_PASSWORD
        sync: false
      - key: REDIS_URL
        sync: false  # Optional: Add Redis service if needed
    healthCheckPath: /api/health

  # Frontend Static Site (if deploying frontend to Render as well)
  - type: web
    name: customer-management-frontend
    env: static
    region: frankfurt
    plan: free
    buildCommand: |
      cd frontend
      npm install
      npm run build
    staticPublishPath: frontend/dist
    envVars:
      - key: VITE_API_URL
        value: https://customer-management-backend.onrender.com
      - key: VITE_FIREBASE_API_KEY
        sync: false
      - key: VITE_FIREBASE_AUTH_DOMAIN
        sync: false
      - key: VITE_FIREBASE_PROJECT_ID
        sync: false
      - key: VITE_FIREBASE_STORAGE_BUCKET
        sync: false
      - key: VITE_FIREBASE_MESSAGING_SENDER_ID
        sync: false
      - key: VITE_FIREBASE_APP_ID
        sync: false
      - key: VITE_FIREBASE_MEASUREMENT_ID
        sync: false
